<x-layouts.master title="Home">

    @push('cssLinks')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/calendar/fa/calendar-fa.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/css/dashboard.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/apex-charts/apex-charts.css') }}" />
        <style>
            #announcements_body tr td {
                text-align: justify;
            }

            .cont1 {
                text-align: center;
                /* border: 1px dashed #aaa; */
                border-radius: 8px;
                padding: 16px;
                background-color: #fff;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .image-placeholder {
                margin-bottom: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

      
        </style>
    @endpush

    @push('content')
    <div class="row">

        <div class="col-lg-8 mb-4 order-0 ">
            <div class="d-flex h-100 rounded  flex-column gap-1">
                <x-pagecontents.attendanceannouncement-bar />
                <div class="col-sm-12">
                    <div class="card">
                        &nbsp;
                        {{-- <div class="row row-bordered g-0">
                                <div class="col-md-8">
                                    <h5 class="card-header m-0 me-2 pb-3"
                                        title="it shows graphical status of employess knowledge degree">
                                        chart for Estikhdam </h5>
                                    <div id="totalRevenueChart" class="px-2"></div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card-body">
                                        <div class="text-center">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-label-primary dropdown-toggle"
                                                    type="button" id="growthReportId" data-bs-toggle="dropdown"
                                                    aria-haspopup="true" aria-expanded="false">
                                                    سویه تحصیلی
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-end"
                                                    aria-labelledby="growthReportId">
                                                    <a class="dropdown-item" href="javascript:void(0);">دوکتورا</a>
                                                    <a class="dropdown-item" href="javascript:void(0);">ماستر</a>
                                                    <a class="dropdown-item" href="javascript:void(0);">لسانس</a>
                                                    <a class="dropdown-item" href="javascript:void(0);">بکلوریا</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="growthChart"></div>
                                    <div class="text-center fw-semibold pt-3 mb-2">some othere
                                        knowledge
                                    </div>

                                    <div
                                        class="d-flex px-xxl-4 px-lg-2 p-4 gap-xxl-3 gap-lg-1 gap-3 justify-content-between">
                                        <div class="d-flex">
                                            <div class="me-2">
                                                <span class="badge bg-label-primary p-2"><i
                                                        class="bx bx-dollar text-primary"></i></span>
                                            </div>
                                            <div class="d-flex flex-column">
                                                <small>total Employees</small>
                                                <h7 class="mb-0">5k</h7>
                                            </div>
                                        </div>
                                        <div class="d-flex">
                                            <div class="me-2">
                                                <span class="badge bg-label-info p-2"><i
                                                        class="bx bx-wallet text-info"></i></span>
                                            </div>
                                            <div class="d-flex flex-column">
                                                <small>Selected</small>
                                                <h7 class="mb-0">1k</h7>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> --}}
                    </div>
                </div>
            </div>

        </div>
        <div class="col-lg-4 mb-4 order-1 ">
            <div class="d-flex bg-white h-100 rounded">
                <div class="col-12">
                    <div class="card h-100 d-flex">
                        <div class="card-body">
                            <x-libs.calendar.fa.calendar />

                        </div>
                    </div>
                </div>
            </div>

        </div>


    </div>

        <div class="row">
            <div class="d-flex gap-4 flex-wrap">
                @can('cards_module')
                    <x-pagecontents.cards-chart />
                @endcan

                @can('employee-attendance_module')
                    <div class=" mb-4 flex-fill flex-grow-1 flex-shrink-0 flex-shrink-sm-1">
                        <div class="card text-center">
                            <div class="card-header d-flex align-items-center justify-content-between pb-0">
                                <div class="card-title mb-0 mb-3">
                                    <h5 class="m-0 me-2"> {{ __('general.attendance_in_out_count') }}</h5>
                                </div>
                            </div>
                            <div class="card-body  d-flex justify-content-center">
                                <div class="max-w-400px ">
                                    <canvas id="doughnutChart" class="chartjs" data-height="350"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                @endcan


            </div>
        </div>

        <div class="row">
            <div class="d-flex gap-4 flex-wrap align-items-stretch">
                <!-- Order Statistics -->
                @can('hr-development_module')
                    <div class=" mb-4 flex-fill flex-grow-2 flex-shrink-0 flex-shrink-sm-1">
                        <div class="card h-100 d-flex flex-column">
                            <div class="card-header d-flex align-items-center justify-content-between pb-0">
                                <div class="card-title mb-0">
                                    <h5 class="m-0 me-2">{{ __('general.basts') }} {{' ('. getCurrentShamsiYear().')'}}</h5>
                                </div>
                            </div>
                            <div class="card-body flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex flex-column align-items-center gap-1">
                                        <h2 class="my-2" id="total_tashkil">0</h2>
                                    </div>
                                    <div id="orderStatisticsChart"></div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <ul class="p-0 m-0">
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-primary"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.mafaowq_bast') }}</h7>

                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="mafaowq_bast_total">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-success"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.faowq_bast') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="faowq_bast_total">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-info"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_1') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_1">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-secondary"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_2') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_2">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-primary"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_3') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_3">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-6">
                                        <ul class="p-0 m-0">
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-success"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_4') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_4">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-info"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_5') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_5">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-secondary"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_6') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_6">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-primary"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_7') }}</h7>

                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_7">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="d-flex mb-2 pb-1">
                                                <div class="avatar flex-shrink-0 me-3">
                                                    <span class="avatar-initial rounded bg-label-success"><i
                                                            class='fa-solid fa-map-pin'></i></span>
                                                </div>
                                                <div class="d-flex  gap-2">
                                                    <div class="me-2">
                                                        <h7 class="mb-0">{{ __('general.bast_8') }}</h7>
                                                    </div>
                                                    <div class="user-progress">
                                                        <small class="fw-semibold" id="bast_8">0</small>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endcan

                <!--/ Order Statistics -->

                <!-- Transactions -->

                <!--/ Transactions -->
                <!-- Activity Timeline -->

                {{-- <div class="col-md-8 col-lg-8 order-4 mb-4"> --}}
                <div class=" mb-4 flex-fill flex-grow-1 flex-shrink-0 flex-shrink-sm-1">
                    <x-pagecontents.forms :forms="$data['forms']" />
                </div>
                <!--/ Activity Timeline -->
                <!-- pill table -->

            </div>


        </div>
    @endpush

    @pushOnce('pscript')
        <script src="{{ asset('assets/js/my/prevent_enter.js') }}"></script>
        <script src="{{ asset('assets/js/my/sidebar_toggler.js') }}"></script>
        <script src="{{ asset('assets/js/my/datatable_padding_toggler.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/chartjs/chartjs.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/apex-charts/apexcharts.js') }}"></script>

        <script>
            let attendanceInCountTitle = '{{ trans('general.attendance_in_count') }}';
            let attendanceOutCountTitle = '{{ trans('general.attendance_out_count') }}';

            $(document).ready(function() {
                async function getAnnouncements() {
                    try {
                        let response = await axiosObj.get('{{ route('attendance.announcements.rendered') }}');

                        if (response != null && response.data != null) {
                            $("#announcements").empty().append(response.data);
                        }
                    } catch (error) {
                        DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                    }
                }
                @can('hr-development_module')

                    async function getTashkilatCount() {
                        try {
                            let response = await axiosObj.get('{{ route('tashkil.count.bast') }}');

                            if (response != null && response.data != null && response.data.data != null && response
                                .data
                                .data.data != null) {
                                $("#total_tashkil").empty().append(response.data.data.data['total_current_year']);
                                $("#mafaowq_bast_total").empty().append(response.data.data.data['mafaoq_basts']);
                                $("#faowq_bast_total").empty().append(response.data.data.data['faoq_basts']);
                                $("#bast_1").empty().append(response.data.data.data['1_basts']);
                                $("#bast_2").empty().append(response.data.data.data['2_basts']);
                                $("#bast_3").empty().append(response.data.data.data['3_basts']);
                                $("#bast_4").empty().append(response.data.data.data['4_basts']);
                                $("#bast_5").empty().append(response.data.data.data['5_basts']);
                                $("#bast_6").empty().append(response.data.data.data['6_basts']);
                                $("#bast_7").empty().append(response.data.data.data['7_basts']);
                                $("#bast_8").empty().append(response.data.data.data['8_basts']);
                            }
                        } catch (error) {
                            DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                        }
                    }
                    getTashkilatCount();
                @endcan
                @can('employee-attendance_module')

                    async function getAttendanceInOutCount() {
                        try {
                            let response = await axiosObj.get('{{ route('attendance.employees.in-out-count') }}');

                            if (response != null && response.data != null && response.data.data != null && response
                                .data
                                .data.data != null) {
                                $('#attendance_in_count').empty().text(response.data.data.data['in']);
                                $('#attendance_out_count').empty().text(response.data.data.data['out']);

                                const cyanColor = '#28dac6',
                                    orangeLightColor = '#FDAC34';
                                let cardColor, headingColor, labelColor, borderColor, legendColor;
                                if (isDarkStyle) {
                                    cardColor = config.colors_dark.cardColor;
                                    headingColor = config.colors_dark.headingColor;
                                    labelColor = config.colors_dark.textMuted;
                                    legendColor = config.colors_dark.bodyColor;
                                    borderColor = config.colors_dark.borderColor;
                                } else {
                                    cardColor = config.colors.cardColor;
                                    headingColor = config.colors.headingColor;
                                    labelColor = config.colors.textMuted;
                                    legendColor = config.colors.bodyColor;
                                    borderColor = config.colors.borderColor;
                                }
                                const doughnutChart = document.getElementById('doughnutChart');

                                if (doughnutChart) {
                                    const doughnutChartVar = new Chart(doughnutChart, {
                                        type: 'doughnut',
                                        data: {
                                            datasets: [{
                                                data: [response.data.data.data['in'], response.data
                                                    .data
                                                    .data['out']
                                                ],
                                                backgroundColor: [cyanColor, orangeLightColor],
                                                borderWidth: 0,
                                                pointStyle: 'rectRounded'
                                            }],
                                            labels: [attendanceInCountTitle, attendanceOutCountTitle]
                                        },
                                        options: {
                                            height: 400,
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            animation: {
                                                duration: 1000
                                            },
                                            cutout: '60%',
                                            plugins: {
                                                legend: {
                                                    display: true
                                                },
                                                tooltip: {
                                                    enabled: true,
                                                    rtl: isRtl,
                                                    backgroundColor: cardColor,
                                                    titleColor: headingColor,
                                                    bodyColor: legendColor,
                                                    borderWidth: 1,
                                                    borderColor: borderColor
                                                }
                                            }
                                        }
                                    });
                                }
                            }
                        } catch (error) {
                            DisplayMessage("{{ trans('general.something_wrong_happened') }}", false);
                        }
                    }
                    getAttendanceInOutCount();
                @endcan

                getAnnouncements();


                const verticalExample = document.getElementById('vertical-example');

                // Vertical Example
                // --------------------------------------------------------------------
                if (verticalExample) {
                    new PerfectScrollbar(verticalExample, {
                        wheelPropagation: false
                    });
                }


            });
        </script>
    @endPushOnce
</x-layouts.master>
