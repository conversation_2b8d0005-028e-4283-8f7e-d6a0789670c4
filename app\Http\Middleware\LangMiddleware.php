<?php

namespace App\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

class LangMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $this->checkUserLang();
        return $next($request);
    }
    private function checkUserLang(): void
    {
        $userLang = auth()->user()?->lang; // null-safe operator

        if ($userLang) {
            // Always sync session with user's language
            session([
                'lang'    => $userLang,
                'langDir' => in_array($userLang, ['dr', 'pa']) ? 'rtl' : 'ltr',
            ]);

            $this->applyLocale($userLang);
        } elseif (Session::has('lang')) {
            // Guest users, or logged-in users with no lang set in DB
            $this->applyLocale(Session::get('lang'));
        }
    }

    private function applyLocale(string $lang): void
    {
        App::setLocale($lang);

        // Define Carbon locale mappings if some differ from app locale
        $carbonLocales = [
            'dr' => 'dr',
            'pa' => 'pa',
            // add others if needed
        ];

        Carbon::setLocale($carbonLocales[$lang] ?? $lang);
    }
}
