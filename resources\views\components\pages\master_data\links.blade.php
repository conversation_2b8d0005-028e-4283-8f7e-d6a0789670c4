<x-slot name="link">
    <span class="flex-fill mt-n3">
        <span class="d-flex gap-1 flex-wrap ">
            <div class=" px-0 mx-0">
                <ul class="list-group list-group-timeline p-0 m-0">
                    @can(['master-data_recruitment'])
                        <li class="list-group-item list-group-timeline-primary p-0 pt-1 px-2">
                            {{-- Recruitment section --}}
                            <span class="badge bg-primary  mb-1">{{ __('general.recruitment') }}</span>
                            <span class="d-flex gap-1 flex-wrap ">
                                <x-pages.link routeName="master-data.ethnicity.all" :activeIn="[
                                    'master-data.ethnicity.all',
                                    'master-data.ethnicity.create',
                                    'master-data.ethnicity.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.ethnicities') }}" />
                                <x-pages.link routeName="master-data.moqarari-approval-authority.all" :activeIn="[
                                    'master-data.moqarari-approval-authority.all',
                                    'master-data.moqarari-approval-authority.create',
                                    'master-data.moqarari-approval-authority.edit',
                                ]"
                                    cssClass="btn-sm" iconName=""
                                    name="{{ __('general.moqarari_approval_authorities') }}" />
                                <x-pages.link routeName="master-data.transfer-reason.all" :activeIn="[
                                    'master-data.transfer-reason.all',
                                    'master-data.transfer-reason.create',
                                    'master-data.transfer-reason.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.transfer_reasons') }}" />
                                <x-pages.link routeName="master-data.country.all" :activeIn="[
                                    'master-data.country.all',
                                    'master-data.country.create',
                                    'master-data.country.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.countries') }}" />
                                <x-pages.link routeName="master-data.province.all" :activeIn="[
                                    'master-data.province.all',
                                    'master-data.province.create',
                                    'master-data.province.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.provinces') }}" />
                                <x-pages.link routeName="master-data.district.all" :activeIn="[
                                    'master-data.district.all',
                                    'master-data.district.create',
                                    'master-data.district.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.districts') }}" />
                                <x-pages.link routeName="master-data.village.all" :activeIn="[
                                    'master-data.village.all',
                                    'master-data.village.create',
                                    'master-data.village.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.villages') }}" />
                                <x-pages.link routeName="master-data.academic-field-category.all" :activeIn="[
                                    'master-data.academic-field-category.all',
                                    'master-data.academic-field-category.create',
                                    'master-data.academic-field-category.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.academic_field_categories') }}" />
                                <x-pages.link routeName="master-data.academic-field.all" :activeIn="[
                                    'master-data.academic-field.all',
                                    'master-data.academic-field.create',
                                    'master-data.academic-field.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.academic_fields') }}" />
                                <x-pages.link routeName="master-data.government-office.all" :activeIn="[
                                    'master-data.government-office.all',
                                    'master-data.government-office.create',
                                    'master-data.government-office.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.government_offices') }}" />
                                <x-pages.link routeName="master-data.contract.suspension-reason.all" :activeIn="[
                                    'master-data.contract.suspension-reason.all',
                                    'master-data.contract.suspension-reason.create',
                                    'master-data.contract.suspension-reason.edit',
                                ]"
                                    cssClass="btn-sm" iconName=""
                                    name="{{ __('general.contract_suspension_reasons') }}" />
                                <x-pages.link routeName="master-data.contract.cancellation-reason.all" :activeIn="[
                                    'master-data.contract.cancellation-reason.all',
                                    'master-data.contract.cancellation-reason.create',
                                    'master-data.contract.cancellation-reason.edit',
                                ]"
                                    cssClass="btn-sm" iconName=""
                                    name="{{ __('general.contract_cancellation_reasons') }}" />
                                <x-pages.link routeName="master-data.fire-reason.all" :activeIn="[
                                    'master-data.fire-reason.all',
                                    'master-data.fire-reason.create',
                                    'master-data.fire-reason.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.employee_fire_reasons') }}" />
                                <x-pages.link routeName="master-data.tanqis-reason.all" :activeIn="[
                                    'master-data.tanqis-reason.all',
                                    'master-data.tanqis-reason.create',
                                    'master-data.tanqis-reason.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.employee_tanqis_reasons') }}" />
                                <x-pages.link routeName="master-data.employee.guarantee.type.all" :activeIn="[
                                    'master-data.employee.guarantee.type.all',
                                    'master-data.employee.guarantee.type.create',
                                    'master-data.employee.guarantee.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.guarantee_types') }}" />
                                <x-pages.link routeName="master-data.employee.guarantee.range.all" :activeIn="[
                                    'master-data.employee.guarantee.range.all',
                                    'master-data.employee.guarantee.range.create',
                                    'master-data.employee.guarantee.range.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.guarantee_ranges') }}" />
                            </span>
                            {{-- end of Recruitment section --}}
                        </li>
                    @endcan

                    @can(['master-data_documents'])
                        <li class="list-group-item list-group-timeline-success p-0 pt-1 px-2">
                            {{-- HR documents section --}}
                            <span class="badge bg-success mb-1">{{ __('general.documents') }}</span>
                            <span class="d-flex gap-1 flex-wrap ">
                                <x-pages.link routeName="master-data.university.all" :activeIn="[
                                    'master-data.university.all',
                                    'master-data.university.create',
                                    'master-data.university.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.universities') }}" />
                                <x-pages.link routeName="master-data.education-degree.all" :activeIn="[
                                    'master-data.education-degree.all',
                                    'master-data.education-degree.create',
                                    'master-data.education-degree.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.education_degrees') }}" />
                                <x-pages.link routeName="master-data.employee.evaluation-result.all" :activeIn="[
                                    'master-data.employee.evaluation-result.all',
                                    'master-data.employee.evaluation-result.create',
                                    'master-data.employee.evaluation-result.edit',
                                ]"
                                    cssClass="btn-sm" iconName=""
                                    name="{{ __('general.employee_evaluation_results') }}" />

                                <x-pages.link routeName="master-data.employee.evaluation-recommendation.all"
                                    :activeIn="[
                                        'master-data.employee.evaluation-recommendation.all',
                                        'master-data.employee.evaluation-recommendation.create',
                                        'master-data.employee.evaluation-recommendation.edit',
                                    ]" cssClass="btn-sm" iconName=""
                                    name="{{ __('general.employee_evaluation_recommendations') }}" />

                                <x-pages.link routeName="master-data.employee.qadam.all" :activeIn="[
                                    'master-data.employee.qadam.all',
                                    'master-data.employee.qadam.create',
                                    'master-data.employee.qadam.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.employee_qadams') }}" />
                                <x-pages.link routeName="master-data.employee.makafat.type.all" :activeIn="[
                                    'master-data.employee.makafat.type.all',
                                    'master-data.employee.makafat.type.create',
                                    'master-data.employee.makafat.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.makafat_types') }}" />
                                <x-pages.link routeName="master-data.employee.punishment.type.all" :activeIn="[
                                    'master-data.employee.punishment.type.all',
                                    'master-data.employee.punishment.type.create',
                                    'master-data.employee.punishment.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.punishment_types') }}" />
                                <x-pages.link routeName="master-data.employee.punishment.reason.all" :activeIn="[
                                    'master-data.employee.punishment.reason.all',
                                    'master-data.employee.punishment.reason.create',
                                    'master-data.employee.punishment.reason.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.punishment_reasons') }}" />

                                <x-pages.link routeName="master-data.employee.askari.type.all" :activeIn="[
                                    'master-data.employee.askari.type.all',
                                    'master-data.employee.askari.type.create',
                                    'master-data.employee.askari.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.askari_types') }}" />
                            </span>
                            {{-- ed of HR documents section --}}
                        </li>
                    @endcan
                    @can(['master-data_attendance'])
                        <li class="list-group-item list-group-timeline-warning p-0 pt-1 px-2">
                            {{-- HR attendance section --}}
                            <span class="badge bg-warning mb-1">{{ __('general.attendance') }}</span>
                            <span class="d-flex gap-1 flex-wrap ">
                                <x-pages.link routeName="master-data.attendance.leave.type.all" :activeIn="[
                                    'master-data.attendance.leave.type.all',
                                    'master-data.attendance.leave.type.create',
                                    'master-data.attendance.leave.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.leave_types') }}" />

                                <x-pages.link routeName="master-data.attendance.leave.request.valid.date.all"
                                    :activeIn="[
                                        'master-data.attendance.leave.request.valid.date.all',
                                        'master-data.attendance.leave.request.valid.date.edit',
                                    ]" cssClass="btn-sm" iconName=""
                                    name="{{ __('attendance/att.leave_request_valid_date') }}" />
                            </span>
                            {{-- end of HR attendance section --}}
                        </li>
                    @endcan
                    @can(['master-data_capacity_building'])
                        <li class="list-group-item list-group-timeline-info p-0 pt-1 px-2">
                            {{-- HR training section --}}
                            <span class="badge bg-info mb-1">{{ __('general.capacity_building') }}</span>
                            <span class="d-flex gap-1 flex-wrap ">
                                <x-pages.link routeName="master-data.employee.training.type.all" :activeIn="[
                                    'master-data.employee.training.type.all',
                                    'master-data.employee.training.type.create',
                                    'master-data.employee.training.type.edit',
                                ]"
                                    cssClass="btn-sm" iconName="" name="{{ __('general.training_types') }}" />
                            </span>
                            {{-- end of HR training section --}}
                        </li>
                    @endcan
                    @can(['master-data_cards'])
                        <li class="list-group-item list-group-timeline-danger p-0 pt-1 px-2">
                            <span class="badge bg-danger mb-1">{{ __('general.cards') }}</span>
                            <span class="d-flex gap-1 flex-wrap ">
                                <x-pages.link routeName="master-data.periods.get" :activeIn="[
                                    'master-data.periods.get',
                                    'master-data.periods.create',
                                    'master-data.periods.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.card_period') }}" />
                                <x-pages.link routeName="master-data.card-types.get" :activeIn="[
                                    'master-data.card-types.get',
                                    'master-data.card-types.create',
                                    'master-data.card-types.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.card_types') }}" />
                            </span>

                        </li>
                    @endcan

                    @can(['master-data_tashkilat'])
                        <li class="list-group-item list-group-timeline-secondary p-0 pt-1 px-2">
                            <span class="badge bg-secondary mb-1">{{ __('general.tashkeel') }}</span>
                            <span class="d-flex gap-1 flex-wrap">
                                <x-pages.link routeName="master-data.nta-grid.all" :activeIn="[
                                    'master-data.nta-grid.all',
                                    'master-data.nta-grid.create',
                                    'master-data.nta-grid.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.nta_grids') }}" />

                                <x-pages.link routeName="master-data.nta-step.all" :activeIn="[
                                    'master-data.nta-step.all',
                                    'master-data.nta-step.create',
                                    'master-data.nta-step.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.nta_steps') }}" />

                                <x-pages.link routeName="master-data.nta-salary.all" :activeIn="[
                                    'master-data.nta-salary.all',
                                    'master-data.nta-salary.create',
                                    'master-data.nta-salary.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.nta_salary') }}" />

                                @if (in_array(auth()->user()->id, [1, 3855]))
                                    <x-pages.link routeName="master-data.job-description-template.all" :activeIn="[
                                        'master-data.job-description-template.all',
                                        'master-data.job-description-template.create',
                                        'master-data.job-description-template.edit',
                                    ]"
                                        cssClass="btn-sm" iconName=""
                                        name="{{ __('general.job_description_templates') }}" />
                                @endif
                            </span>
                        </li>
                    @endcan

                    {{-- set permission for software developer --}}
                    @if (in_array(auth()->user()->id, [1, 3855]))
                        <li class="list-group-item list-group-timeline-dark p-0 pt-1 px-2">
                            <span class="badge bg-dark mb-1">{{ __('general.software_developer') }}</span>
                            <span class="d-flex gap-1 flex-wrap">
                                <x-pages.link routeName="master-data.forms.all" :activeIn="[
                                    'master-data.forms.all',
                                    'master-data.form.create',
                                    'master-data.form.edit',
                                ]" cssClass="btn-sm"
                                    iconName="" name="{{ __('general.forms') }}" />
                            </span>
                        </li>
                    @endif

                    {{-- <li class="list-group-item list-group-timeline-secondary">block#</li> --}}
                    {{-- <li class="list-group-item list-group-timeline-dark">block#</li>  --}}
                </ul>
            </div>
        </span>
    </span>







</x-slot>
