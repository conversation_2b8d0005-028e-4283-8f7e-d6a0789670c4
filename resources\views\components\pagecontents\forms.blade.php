@props(['forms' => []])
<div class="card h-100 d-flex flex-column">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title m-0 me-2">{{ __('general.forms') }}</h5>
    </div>
    <div class="card-body flex-grow-1">
        <ul class="p-0 m-0">
           {{-- Special case: Job Description Form (remains static) --}}
            <li class="d-flex pb-1">
                <div class="avatar flex-shrink-0 me-3">
                    <img src="{{ asset('assets/img/icons/unicons/wallet.png') }}" alt="User" class="rounded">
                </div>
                <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2 border-bottom">
                    <div class="me-2">
                        <h7 class="mb-0">{{ __('general.job_description') }}</h7>
                    </div>
                    <div class="user-progress d-flex align-items-center gap-1">
                        <h6 class="mb-0"></h6>
                        <a href="{{ route('forms.download.job-description', ['type' => encrypt('job-description')]) }}"
                            target="_blank" title="{{ __('general.open_in_new_tab') }}">
                            <button class="btn btn-xs rounded-pill btn-label-secondary p-1 px-2">
                                {{ __('general.download') }}
                            </button>
                        </a>
                    </div>
                </div>
            </li>
            {{-- Dynamic forms from database --}}
            @if(isset($forms) && $forms->count() > 0)
                @foreach($forms as $form)
                    <li class="d-flex pb-1">
                        <div class="avatar flex-shrink-0 me-3">
                            <img src="{{ asset('assets/img/icons/unicons/wallet.png') }}" alt="User" class="rounded">
                        </div>
                        <div class="d-flex w-100 flex-wrap align-items-center justify-content-between gap-2 border-bottom">
                            <div class="me-2">
                                <h7 class="mb-0">{{ $form->name }}</h7>
                            </div>
                            <div class="user-progress d-flex align-items-center gap-1">
                                <h6 class="mb-0"></h6>
                                <a href="{{ route('form.download', ['id' => encrypt($form->id)]) }}"
                                    target="_blank" title="{{ __('general.open_in_new_tab') }}">
                                    <button class="btn btn-xs rounded-pill btn-label-secondary p-1 px-2">
                                        {{ __('general.download') }}
                                    </button>
                                </a>
                            </div>
                        </div>
                    </li>
                @endforeach
            @endif
        </ul>
    </div>
</div>
